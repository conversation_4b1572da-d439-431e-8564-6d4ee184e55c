from pathlib import Path

from affinda import AffindaAPI, TokenCredential

token = "aff_f41236189a3ebf3c1723c73a87d48e2ae679e911"
collection_identifier = "IDMKPvJF"

credential = TokenCredential(token=token)
client = AffindaAPI(credential=credential)

file_pth = Path("Mostafa SW.pdf")
with open(file_pth, "rb") as f:
    document = client.create_document(file=f, collection=collection_identifier)

print(document.as_dict())