from flask import Flask, request, jsonify
from test_laia_api import extract_skills_from_job_description
from search import search_resumes_with_skills

app = Flask(__name__)

@app.route('/api/extract-skills', methods=['POST'])
def extract_skills():
    """
    Endpoint to extract skills from a job description.
    Accepts a JSON payload: {"job_description": "..."}
    Returns a JSON list of skills: {"skills": ["skill1", "skill2", ...]}
    """
    data = request.get_json()
    if not data or 'job_description' not in data:
        return jsonify({"error": "Missing 'job_description' in request body"}), 400

    job_description = data['job_description']
    
    # This function now comes from test_laia_api.py
    extracted_skills = extract_skills_from_job_description(job_description)

    if not extracted_skills:
        return jsonify({"error": "Could not extract skills from the provided text."}), 500

    return jsonify({"skills": extracted_skills})

@app.route('/api/search', methods=['POST'])
def search_resumes():
    """
    Endpoint to search for resumes based on a list of skills.
    Accepts a JSON payload: {"skills": ["skill1", "skill2", ...]}
    Returns a JSON list of resume search results.
    """
    data = request.get_json()
    if not data or 'skills' not in data or not isinstance(data['skills'], list):
        return jsonify({"error": "Missing 'skills' list in request body"}), 400

    skills_to_search = data['skills']
    
    # This function now comes from search.py and returns JSON-ready data
    search_results = search_resumes_with_skills(skills_to_search)

    if "error" in search_results:
        return jsonify(search_results), 500

    return jsonify(search_results)

if __name__ == '__main__':
    # Running in debug mode is not recommended for production
    # Use a production-ready WSGI server like Gunicorn instead
    app.run(debug=True, port=5001) 