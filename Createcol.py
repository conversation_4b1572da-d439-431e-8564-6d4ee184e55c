import requests

url = "https://api.affinda.com/v3/collections"
api_token = "aff_f41236189a3ebf3c1723c73a87d48e2ae679e911"
workspace_id = "WtwiyFEU"

payload = {
    "name": "My Resume Collection",
    "workspace": workspace_id,
    "extractor": "resume",
    "enableAutoValidationThreshold": False,
    "allowOpenai": False,
    "disableConfirmationIfValidationRulesFail": False
}
headers = {
    "accept": "application/json",
    "content-type": "application/json",
    "Authorization": f"Bearer {api_token}"
}

response = requests.post(url, json=payload, headers=headers)

print(response.text)