import os
from pathlib import Path
import shutil
from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.responses import J<PERSON>NR<PERSON>ponse
import uvicorn

# Assuming pipline.py is in the same directory
from pipline import CVMatchSystem

# --- Configuration ---
# It's better to load these from environment variables or a config file in a real application
API_KEY = "aff_f41236189a3ebf3c1723c73a87d48e2ae679e911"  # Replace with your actual API key
WORKSPACE_ID = "IDMKPvJF"  # Replace with your actual Workspace ID, or where you store it

# --- FastAPI App Initialization ---
app = FastAPI(
    title="CV Match API",
    description="API for uploading and processing CVs using Affinda.",
    version="0.1.0"
)

# --- CVMatchSystem Initialization ---
try:
    cv_system = CVMatchSystem(api_key=API_KEY, workspace_id=WORKSPACE_ID)
except ValueError as e:
    # Handle cases where API_KEY might be missing if not hardcoded
    print(f"Error initializing CVMatchSystem: {e}")
    # You might want to exit or raise a more specific FastAPI startup error
    # For now, we'll let it proceed, but endpoints might fail if cv_system is not initialized.
    cv_system = None 

# --- Temporary Upload Directory ---
UPLOAD_DIR = Path("temp_uploads")
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

@app.post("/upload-cv/", tags=["CV Management"])
async def upload_cv_endpoint(file: UploadFile = File(...)):
    """
    Uploads a CV (PDF or DOCX), processes it through Affinda,
    and returns the document identifier.
    """
    if not cv_system:
        raise HTTPException(status_code=500, detail="CV Processing System not initialized.")

    if not file.filename:
        raise HTTPException(status_code=400, detail="No file name provided.")

    # Secure filename and create a temporary path
    # For simplicity, we're using the original filename. In production, generate unique names.
    temp_file_path = UPLOAD_DIR / file.filename
    
    try:
        # Save the uploaded file temporarily
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # Process the document using CVMatchSystem
        # create_document should return the document ID
        document_id = cv_system.create_document(file_path=str(temp_file_path))
        
        return JSONResponse(
            status_code=200,
            content={
                "message": "CV uploaded and processed successfully.",
                "document_id": document_id,
                "filename": file.filename
            }
        )

    except FileNotFoundError:
        raise HTTPException(status_code=404, detail=f"Temporary file not found. This is an internal error.")
    except ValueError as ve: # Catch errors from create_document, e.g., Affinda API issues or no doc ID
        raise HTTPException(status_code=500, detail=f"Error processing CV: {str(ve)}")
    except Exception as e:
        # Log the exception e for debugging
        print(f"An unexpected error occurred: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred during CV processing: {str(e)}")
    finally:
        # Clean up the temporary file
        if temp_file_path.exists():
            os.remove(temp_file_path)
        # Close the uploaded file
        await file.close()

@app.get("/", tags=["General"])
async def read_root():
    """
    Root endpoint for the API.
    """
    return {"message": "Welcome to the CV Match API. Use /docs for API documentation."}

# --- Main Execution ---
if __name__ == "__main__":
    print("Starting FastAPI server...")
    print(f"API Key used: {'*' * (len(API_KEY) - 4) + API_KEY[-4:] if API_KEY else 'Not Set'}")
    print(f"Workspace ID used: {WORKSPACE_ID if WORKSPACE_ID else 'Not Set'}")
    if not cv_system:
        print("WARNING: CVMatchSystem failed to initialize. Endpoints requiring it will fail.")
    
    uvicorn.run(app, host="0.0.0.0", port=8000) 