import requests
import json
import os
# Import for search.py - will be added later
# from search import search_resumes_with_skills # Placeholder

# LAIA API endpoint
API_URL = "https://us-central1-experto-aila.cloudfunctions.net/ai-proxy-chat"
AUTH_TOKEN = None # Cache the auth token

# Example payload - kept for reference or if chat_with_laia is reused
# payload = {
#     "messages": [
#         {"role": "user", "content": "What are the legal implications of data privacy laws?"},
#         {"role": "assistant", "content": "Data privacy laws protect..."},
#         {"role": "user", "content": "How does this affect small businesses?"}
#     ],
#     "temperature": 0.5,
#     "max_tokens": 1500,
#     "top_p": 0.9
# }

def get_auth_token():
    """
    Gets and caches the gcloud auth token.
    Tries gcloud command first, then falls back to an environment variable.
    """
    global AUTH_TOKEN
    if AUTH_TOKEN:
        return AUTH_TOKEN
    
    # 1. Try gcloud command
    try:
        token = os.popen('gcloud auth print-identity-token').read().strip()
        if token:
            print("Successfully obtained token via gcloud command.")
            AUTH_TOKEN = token
            return AUTH_TOKEN
        # If token is empty, fall through to the next method.
    except Exception as e:
        print(f"Gcloud command failed: {e}. Trying environment variable.")

    # 2. Fallback to environment variable
    token = os.environ.get('GCLOUD_AUTH_TOKEN')
    if token:
        print("Successfully obtained token from GCLOUD_AUTH_TOKEN environment variable.")
        AUTH_TOKEN = token
        return AUTH_TOKEN

    print("Warning: Could not obtain auth token via gcloud or environment variable.")
    print("API calls requiring authentication may fail.")
    return None

def call_laia_api(messages_list, temperature=0.5, max_tokens=1500, top_p=0.9):
    """Calls the LAIA API with the given messages and parameters."""
    global AUTH_TOKEN
    headers = {"Content-Type": "application/json"}
    
    # Prepare payload for all API calls
    request_payload_data = {
        "messages": messages_list,
        "temperature": temperature,
        "max_tokens": max_tokens,
        "top_p": top_p
    }

    # Attempt 1: Try with existing AUTH_TOKEN if available
    if AUTH_TOKEN:
        headers["Authorization"] = f"bearer {AUTH_TOKEN}"
        try:
            response = requests.post(
                API_URL,
                headers=headers,
                data=json.dumps(request_payload_data),
                timeout=130 
            )
            if response.status_code == 200:
                return response.json().get("response")
            elif response.status_code == 401: # Token might be expired or invalid
                print("Authentication failed with existing token. Attempting to refresh token.")
                AUTH_TOKEN = None # Clear potentially invalid token
            else: # Other error with existing token
                return f"Error with existing token: {response.status_code} - {response.text}"
        except requests.exceptions.RequestException as e:
            return f"Error during request with existing token: {str(e)}"
        except json.JSONDecodeError:
            return f"Error: Could not decode JSON response from API (with existing token). Response: {response.text}"


    # Attempt 2: Try without authentication OR if AUTH_TOKEN was cleared due to 401
    if not AUTH_TOKEN: # This condition is met if AUTH_TOKEN was None initially, or cleared after a 401
        plain_headers = {"Content-Type": "application/json"}
        try:
            response = requests.post(
                API_URL,
                headers=plain_headers,
                data=json.dumps(request_payload_data),
                timeout=30
            )
            if response.status_code == 200:
                return response.json().get("response")
            elif response.status_code == 401:
                print("API requires authentication. Attempting to get auth token...")
                new_token = get_auth_token() # This will update global AUTH_TOKEN if successful
                if not AUTH_TOKEN: # Check global AUTH_TOKEN as get_auth_token updates it
                    return "Error: Authentication required and failed to obtain token."
                
                # Retry with the new token
                headers["Authorization"] = f"bearer {AUTH_TOKEN}"
                response = requests.post(
                    API_URL,
                    headers=headers,
                    data=json.dumps(request_payload_data),
                    timeout=130
                )
                if response.status_code == 200:
                    return response.json().get("response")
                else:
                    return f"Error after obtaining new token: {response.status_code} - {response.text}"
            else: # Other error without authentication
                return f"Error: {response.status_code} - {response.text}"
        except requests.exceptions.RequestException as e:
            return f"Error during request (unauthenticated/token acquisition): {str(e)}"
        except json.JSONDecodeError:
             return f"Error: Could not decode JSON response from API (unauthenticated). Response: {response.text}"
    
    return "Error: API call failed through all authentication attempts."


def extract_skills_from_job_description(job_description):
    """
    Sends a job description to the LAIA API to extract skills.
    Assumes skills are returned as a comma-separated string by the API.
    """
    prompt_message = (
        "Extract the key skills from the following job description. "
        "Please list them clearly, separated by commas. For example: Python, Java, Project Management. "
        "Do not add any other text before or after the comma-separated list of skills. " # Added for clarity
        "Job Description:\n---\n"
        f"{job_description}\n---"
    )
    
    messages = [{"role": "user", "content": prompt_message}]
    
    # Lower temperature for more deterministic/factual skill extraction
    api_response_content = call_laia_api(messages, temperature=0.2, max_tokens=300) 
    
    if api_response_content and not api_response_content.startswith("Error:"):
        # Assuming skills are comma-separated. Robust parsing might be needed.
        skills = [skill.strip() for skill in api_response_content.split(',') if skill.strip()]
        return skills
    else:
        print(f"\nFailed to extract skills. API response: {api_response_content}")
        return []

def chat_with_laia():
    messages = []
    print("LAIA Chatbot - Type 'quit' to exit\n")
    
    while True:
        user_input = input("You: ")
        if user_input.lower() == 'quit':
            break
            
        messages.append({"role": "user", "content": user_input})
        
        try:
            # First try without authentication
            headers = {
                "Content-Type": "application/json"
            }
            
            request_payload = {"messages": messages}
            
            # The API call logic is now in call_laia_api
            # We need to decide if chat_with_laia will use the new call_laia_api or keep its own logic.
            # For simplicity, let's adapt it to use call_laia_api.
            
            assistant_response_content = call_laia_api(messages) # Default temp, tokens, etc.

            if assistant_response_content and not assistant_response_content.startswith("Error:"):
                print(f"\nLAIA: {assistant_response_content}")
                messages.append({"role": "assistant", "content": assistant_response_content})
            else:
                print(f"\nLAIA API Error: {assistant_response_content}")
                # Decide if we want to break or allow retry, or clear messages
                # For now, just print error and continue loop
        except Exception as e: # General exception for input or other unexpected issues
            print(f"\nError in chat loop: {str(e)}")

if __name__ == "__main__":
    # Attempt to import the search function from search.py
    search_resumes_with_skills = None
    try:
        from search import search_resumes_with_skills # We will define this function in search.py
        print("Successfully imported 'search_resumes_with_skills' from search.py")
    except ImportError:
        print("Warning: Could not import 'search_resumes_with_skills' from search.py.")
        print("Please ensure search.py is in the same directory and contains this function.")
        print("CV search functionality will be unavailable.")
    except AttributeError:
        print("Warning: 'search_resumes_with_skills' function not found in search.py.")
        print("Please ensure search.py defines this function.")
        print("CV search functionality will be unavailable.")


    print("\n--- Job Description to CV Search ---")
    job_desc = input("Please paste the job description here (or press Enter to skip skill extraction and try chat mode):\n")
    
    if job_desc.strip():
        print("\nExtracting skills from job description...")
        extracted_skills = extract_skills_from_job_description(job_desc)
        
        if extracted_skills:
            print("\n--- Extracted Skills ---")
            for i, skill in enumerate(extracted_skills):
                print(f"{i+1}. {skill}")
            
            while True:
                selected_skills_indices_str = input(
                    "\nEnter the numbers of the skills you want to search for (e.g., 1, 3, 4), "
                    "type 'all' to use all, or 'none' to skip CV search: "
                ).strip().lower()
                
                final_skills_to_search = []
                if selected_skills_indices_str == 'all':
                    final_skills_to_search = extracted_skills
                    break
                elif selected_skills_indices_str == 'none':
                    print("Skipping CV search.")
                    break
                else:
                    try:
                        indices = [int(i.strip()) - 1 for i in selected_skills_indices_str.split(',') if i.strip()]
                        valid_indices = True
                        for index in indices:
                            if 0 <= index < len(extracted_skills):
                                if extracted_skills[index] not in final_skills_to_search: # Avoid duplicates
                                    final_skills_to_search.append(extracted_skills[index])
                            else:
                                print(f"Warning: Invalid skill number {index+1} ignored.")
                                valid_indices = False # Mark that at least one was invalid
                        if final_skills_to_search and valid_indices: # Proceed if at least one valid skill chosen and all inputs were parseable
                             break
                        elif not final_skills_to_search and valid_indices: # All numbers were invalid but parseable
                            print("No valid skills selected from the provided numbers. Please try again.")
                        elif not valid_indices and final_skills_to_search: # Some valid, some invalid
                            print("Some skill numbers were invalid. Continuing with valid selections.")
                            break
                        elif not final_skills_to_search and not valid_indices : # All invalid
                             print("No valid skills selected from the provided numbers. Please try again.")
                        # If only invalid numbers were entered, loop continues

                    except ValueError:
                        print("Invalid input format. Please use numbers separated by commas (e.g., 1,2,3), 'all', or 'none'.")

            if final_skills_to_search:
                print(f"\nSearching for CVs with skills: {', '.join(final_skills_to_search)}")
                if search_resumes_with_skills:
                     search_resumes_with_skills(final_skills_to_search) 
                else:
                    print("CV Search function is not available (failed to import from search.py).")
            elif selected_skills_indices_str != 'none': # if not 'none' and no skills, means something went wrong or user entered nothing useful.
                print("No skills were selected for CV search.")
        else:
            print("Could not extract any skills from the provided job description.")
    else:
        print("\nNo job description provided. Starting general LAIA chatbot.")
        print("You can type 'quit' to exit the chat.")
        chat_with_laia() # Fallback to general chat if no job description
