from affinda import AffindaAP<PERSON>, TokenCredential
from affinda.models import ResumeSearchParameters
import textwrap # For formatting long text blocks

# It's good practice to load sensitive info like tokens from environment variables or config files
# For this example, we'll keep it as is, but flag for future improvement.
TOKEN = "aff_f41236189a3ebf3c1723c73a87d48e2ae679e911" # Renamed for clarity

def search_resumes_with_skills(skill_list: list):
    """
    Searches for resumes using the Affinda API and returns the results as a list of dicts.
    """
    if not skill_list:
        print("No skills provided for search.")
        return []

    try:
        credential = TokenCredential(token=TOKEN)
        client = AffindaAPI(credential=credential)

        formatted_skills = [{"name": skill.strip()} for skill in skill_list if isinstance(skill, str) and skill.strip()]
        
        if not formatted_skills:
            print("No valid skills to search for after formatting.")
            return []

        parameters = ResumeSearchParameters(
            indices=["Resume-Search-Demo"],
            skills=formatted_skills,
        )
        
        print(f"Searching Affinda for resumes with skills: {[s['name'] for s in formatted_skills]}...")
        resp = client.create_resume_search(parameters)

        count = resp.count
        results = resp.results
        
        print(f"API returned {count} resume(s).")

        if not results:
            return []

        # Process results into a list of dictionaries
        processed_results = []
        for result in results:
            result_dict = result.as_dict()
            
            # Shorten the excessively long PDF URL for cleaner output
            pdf_link = result_dict.get('pdf', 'N/A')
            if pdf_link and '?' in pdf_link:
                pdf_link = pdf_link.split('?')[0]

            job_title_info = result_dict.get('job_title', {})
            job_title = job_title_info.get('value', 'N/A') if isinstance(job_title_info, dict) else (job_title_info or 'N/A')
            
            skills_display = "N/A"
            skills_data = result_dict.get('skills')
            if skills_data and isinstance(skills_data, dict):
                skills_list = skills_data.get('value', [])
                if skills_list:
                    if all(isinstance(s, dict) and 'name' in s for s in skills_list):
                        skills_display = ", ".join([s.get('name', '') for s in skills_list if s.get('name')])
                    elif all(isinstance(s, str) for s in skills_list):
                        skills_display = ", ".join(skills_list)

            education_display = "N/A"
            education_data = result_dict.get('education', {})
            if education_data and isinstance(education_data, dict):
                 education_display = education_data.get('value', 'N/A')

            processed_results.append({
                "name": result_dict.get('name', 'N/A'),
                "score": result_dict.get('score', 0),
                "job_title": job_title,
                "skills": skills_display,
                "education": education_display,
                "pdf": pdf_link,
                "identifier": result_dict.get('identifier')
            })
        return processed_results
            
    except Exception as e:
        print(f"An error occurred during resume search: {e}")
        return {"error": str(e)}

def display_results_in_console(results: list):
    """Takes a list of processed result dictionaries and prints them nicely."""
    if not results:
        print("No results to display.")
        return

    print("\n" + "="*40)
    print(f"      Found {len(results)} matching resume(s)")
    print("="*40 + "\n")

    for i, result in enumerate(results):
        print(f"--- Result {i+1} " + "-"*29)
        print(f"  Name: {result.get('name', 'N/A')}")
        print(f"  Score: {result.get('score', 0):.2f}")
        print(f"  Job Title: {result.get('job_title', 'N/A')}")
        
        print("  Skills:")
        wrapped_skills = textwrap.fill(result.get('skills', 'N/A'), width=70, initial_indent="    ", subsequent_indent="    ")
        print(wrapped_skills)

        print("  Education:")
        education_display = textwrap.fill(result.get('education', 'N/A'), width=70, initial_indent="    ", subsequent_indent="    ")
        print(education_display)
        
        print(f"  PDF: {result.get('pdf', 'N/A')}")
        print("-" * 40 + "\n")

# Example of how to run this if executed directly (for testing)
if __name__ == "__main__":
    print("Testing resume search with example skills...")
    test_skills = ["Python", "Data Analysis", "Machine Learning"]
    search_results = search_resumes_with_skills(test_skills)
    
    if search_results:
        display_results_in_console(search_results)

