import os
from pathlib import Path
from typing import List, Dict, Any
import requests
from affinda import AffindaAPI, TokenCredential


class CVMatchSystem:
    def __init__(self, api_key: str, workspace_id: str):
        if not api_key:
            raise ValueError("API key must be provided")
        self.workspace_id = workspace_id
        self.api_key = api_key  # Store the API key directly
        # Initialize Affinda SDK client
        credential = TokenCredential(token=api_key)
        self.client = AffindaAPI(credential=credential)
        # Base URL for additional endpoints
        self.base_url = "https://api.affinda.com/v3"

    def create_document(self, file_path: str) -> str:
        """
        Upload a resume document via the Affinda SDK, display a summary, and return the document ID.
        """
        print(f"Parsing resume: {file_path}")
        with open(file_path, "rb") as f:
            # We're using collection ID directly instead of workspace for better parsing
            document = self.client.create_document(
                file=f, 
                collection="IDMKPvJF"  # Hard-coded collection ID that we know works for resumes
            )
        
        # Display a concise summary of the parsed resume
        if hasattr(document, 'data'):
            data = document.data
            
            print("\n===== RESUME SUMMARY =====")
            
            # Personal info
            if hasattr(data, 'name'):
                if isinstance(data.name, dict):
                    name_parts = []
                    for part in ['first', 'middle', 'last']:
                        if part in data.name and data.name[part]:
                            name_parts.append(data.name[part])
                    full_name = " ".join(name_parts)
                    print(f"Name: {full_name}")
                else:
                    print(f"Name: {data.name}")
                    
            if hasattr(data, 'emails') and data.emails:
                print(f"Email: {data.emails[0]}")
                
            if hasattr(data, 'phone_numbers') and data.phone_numbers:
                print(f"Phone: {data.phone_numbers[0]}")
            
            # Education
            if hasattr(data, 'education') and data.education:
                print("\n--- Education ---")
                for edu in data.education[:2]:  # Limit to first 2 entries
                    org = getattr(edu, 'organization', 'Unknown Institution')
                    accred = getattr(edu, 'accreditation', None)
                    degree = getattr(accred, 'education', 'Degree') if accred else 'Degree'
                    print(f"• {org}: {degree}")
            
            # Work Experience
            if hasattr(data, 'work_experience') and data.work_experience:
                print("\n--- Work Experience ---")
                for job in data.work_experience[:2]:  # Limit to first 2 entries
                    org = getattr(job, 'organization', '')
                    title = getattr(job, 'job_title', '')
                    job_info = f"{title}"
                    if org:
                        job_info += f" at {org}"
                    print(f"• {job_info}")
            
            # Skills - just show 5 top skills
            if hasattr(data, 'skills') and data.skills:
                print("\n--- Top Skills ---")
                skills = [getattr(skill, 'name', '') for skill in data.skills 
                         if hasattr(skill, 'name')][:5]
                for skill in skills:
                    print(f"• {skill}")
            
            print("===========================\n")
        
        # Get the document identifier for indexing
        doc_id = None
        if hasattr(document, 'identifier'):
            doc_id = document.identifier
        else:
            # Try to extract from the meta data if available
            meta = getattr(document, 'meta', None)
            if meta and hasattr(meta, 'identifier'):
                doc_id = meta.identifier
        
        if not doc_id:
            raise ValueError("Could not extract document ID from the parsed resume")
            
        return doc_id

    def create_index(self, index_name: str) -> Dict[str, Any]:
        """
        Create a new index for resume search using the REST API.
        """
        url = f"{self.base_url}/index"
        payload = {
            "name": index_name,
            "workspace": self.workspace_id,
            "documentType": "resume"
        }
        response = requests.post(
            url,
            headers={"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"},
            json=payload
        )
        response.raise_for_status()
        return response.json().get("data", {})

    def index_document(self, index_name: str, document_id: str) -> None:
        """
        Index a parsed document into the given index via the REST API.
        """
        url = f"{self.base_url}/index/{index_name}/documents"
        payload = {"documentId": document_id}
        response = requests.post(
            url,
            headers={"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"},
            json=payload
        )
        response.raise_for_status()

    def search_resumes(self, index_name: str, custom_criteria: List[Dict[str, Any]], limit: int = 20) -> Dict[str, Any]:
        """
        Search through parsed resumes with custom criteria via the REST API.
        Returns matching resume IDs and scores.
        """
        url = f"{self.base_url}/resume_search"
        payload = {
            "indices": [index_name],
            "customCriteria": custom_criteria,
            "limit": limit
        }
        response = requests.post(
            url,
            headers={"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"},
            json=payload
        )
        response.raise_for_status()
        return response.json()

    def get_search_details(self, search_id: str) -> Dict[str, Any]:
        """
        Get detailed breakdown of matched criteria per resume.
        """
        url = f"{self.base_url}/resume_search/details/{search_id}"
        response = requests.post(
            url,
            headers={"Authorization": f"Bearer {self.api_key}"}
        )
        response.raise_for_status()
        return response.json()

    def get_document(self, document_id: str) -> Dict[str, Any]:
        """
        Fetch the full parsed document data via the REST API.
        """
        url = f"{self.base_url}/documents/{document_id}"
        response = requests.get(
            url,
            headers={"Authorization": f"Bearer {self.api_key}"}
        )
        response.raise_for_status()
        return response.json().get("data", {})

if __name__ == "__main__":
    # Load configuration from environment variables
    API_KEY = "aff_f41236189a3ebf3c1723c73a87d48e2ae679e911"
    WORKSPACE_ID = "IDMKPvJF"

    system = CVMatchSystem(API_KEY, WORKSPACE_ID)

    # Example: parse resumes from the 'resumes' directory
    resume_folder = Path("resumes")
    
    # Check if the resumes directory exists, create it if it doesn't
    if not resume_folder.exists():
        print(f"Creating directory '{resume_folder}'...")
        resume_folder.mkdir()
        print(f"Please place resume PDF files in the '{resume_folder}' directory and run this script again.")
        exit()
    
    # Get all PDF files in the resumes directory
    resume_files = list(resume_folder.glob("*.pdf"))
    
    if not resume_files:
        print(f"No PDF files found in '{resume_folder}' directory.")
        print("Please add some resume PDFs and run this script again.")
        exit()
    
    print(f"Found {len(resume_files)} resume(s) to process.")
    
    # Parse each resume
    parsed_docs = []
    for resume_file in resume_files:
        try:
            print(f"\nProcessing {resume_file.name}...")
            doc_id = system.create_document(str(resume_file))
            parsed_docs.append({"file": resume_file.name, "id": doc_id})
            print(f"Successfully parsed resume: {resume_file.name} (ID: {doc_id})")
        except Exception as e:
            print(f"Error processing {resume_file.name}: {e}")
    
    print(f"\nSuccessfully parsed {len(parsed_docs)} out of {len(resume_files)} resumes.")

